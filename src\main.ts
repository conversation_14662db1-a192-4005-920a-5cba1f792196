import { createApp, nextTick } from "vue"
import App from "./App.vue"
import store from "./store"
import router from "./router"
import "./assets/css/nucleo-icons.css"
import "./assets/css/nucleo-svg.css"

// Bootstrap JS (necessário para componentes interativos como input-groups, dropdowns, etc.)
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// Vuetify
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

import MaterialDashboard from "./material-dashboard"

import '@mdi/font/css/materialdesignicons.css'

import './assets/css/lumi.css'
import './assets/css/lumi-bootstrap-override.css'
import './assets/css/lumi-compact.css'
import './assets/scss/lumi-toast.scss'

import { LumiCalendar } from "./views/components/LumiCalendar";

import VioletJs from './plugins/VioletJs'

import i18n from './i18n'


import Vue3EasyDataTable from 'vue3-easy-data-table';
import 'vue3-easy-data-table/dist/style.css';

import { library } from "@fortawesome/fontawesome-svg-core";
import { fas } from '@fortawesome/free-solid-svg-icons'
import { fab } from '@fortawesome/free-brands-svg-icons'

import filters from './helpers/filters'

import moment from 'moment-timezone'

import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'

moment.tz.setDefault('America/Sao_Paulo')

library.add(fas);
library.add(fab);

const appInstance = createApp(App)
appInstance.use(store)
appInstance.use(router)
appInstance.use(MaterialDashboard)
appInstance.use(LumiCalendar)
appInstance.use(VioletJs)
appInstance.use(i18n)
appInstance.use(VueViewer)

appInstance.config.globalProperties.$filters = filters

// Add global mixin to inject userInfo computed property in all components
appInstance.mixin({
  computed: {
    $user() {
      const token = this.$store?.state?.token;
      return token ? { ...token, system_admin: Boolean(token.system_admin) } : null;
    },

    $clinica() {
      return this.$store?.state?.token?.clinica || null;
    },
    $dentista() {
        return this.$store?.state?.token?.dentista || null;
    }
  }
});

const vuetify = createVuetify({
    components,
    directives,
})

appInstance.use(vuetify)

appInstance.mount("#app")
appInstance.component('EasyDataTable', Vue3EasyDataTable)

// Função para esconder o loading inicial
const hideInitialLoader = () => {
  const initialLoader = document.getElementById('app-initial-loader')
  if (initialLoader) {
    initialLoader.classList.add('fade-out')
    // Remover o elemento do DOM após a animação
    setTimeout(() => {
      initialLoader.remove()
    }, 500)
  }
}

// Esconder o loading inicial após a aplicação estar montada
nextTick(() => {
  // Aguardar um pouco mais para garantir que todos os recursos carregaram
  // Reduzindo o tempo para melhorar a experiência do usuário
  setTimeout(hideInitialLoader, 800)
})

// Também esconder se a página já estiver carregada (fallback)
if (document.readyState === 'complete') {
  setTimeout(hideInitialLoader, 600)
} else {
  window.addEventListener('load', () => {
    setTimeout(hideInitialLoader, 400)
  })
}

// Fallback adicional para garantir que o loader seja removido
setTimeout(() => {
  const loader = document.getElementById('app-initial-loader')
  if (loader) {
    console.warn('Removendo loader inicial por timeout de segurança')
    hideInitialLoader()
  }
}, 3000) // 3 segundos como último recurso

// appInstance.component('font-awesome-icon', FontAwesomeIcon);
